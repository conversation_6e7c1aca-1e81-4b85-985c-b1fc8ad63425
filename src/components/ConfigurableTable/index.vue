<template>
  <div class="configurable-table">
    <!-- 普通工具栏 -->
    <div v-show="!shouldShowSelectionToolbar" class="table-toolbar-wrapper">
      <div class="table-toolbar-left">
        <a-space>
          <slot name="toolbar-left"></slot>
          <!-- 列配置组件 -->
          <ColumnConfig
            v-if="showColumnConfig"
            :columns="props.columns"
            @changeVisibleColumns="handleColumnsUpdate"
          />
          <!-- 行高配置组件 -->
          <LineHeight v-if="showLineHeight" @change="handleLineHeightChange" />
        </a-space>
      </div>
      <div class="table-toolbar-right">
        <a-space>
          <slot name="toolbar-right"></slot>
        </a-space>
      </div>
    </div>

    <!-- 选择工具栏 -->
    <div v-show="shouldShowSelectionToolbar" class="selection-toolbar-wrapper">
      <div class="selection-toolbar-left">
        <a-space>
          <span>
            已选择
            <span style="font-weight: 500">{{ selectedRowKeys.length }}</span>
            条
          </span>
          <slot name="selection-toolbar-left"></slot>
        </a-space>
      </div>
      <div class="selection-toolbar-right">
        <a-button type="default" :icon="h(UndoOutlined)" @click="clearSelection()">
          撤销多选
        </a-button>
      </div>
    </div>

    <!-- 表格主体 -->
    <a-table
      :id="tableId"
      :row-key="rowKey"
      :row-selection="tableRowSelection"
      :scroll="scroll"
      :columns="tableColumns"
      :data-source="dataSource"
      :loading="loading"
      :customRow="mergeRowStyle"
      :pagination="tablePagination"
      bordered
      v-bind="$attrs"
    >
      <!-- 传递所有插槽 -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>

      <!-- 默认操作列 -->
      <template #bodyCell="{ column, record, index }">
        <slot name="bodyCell" :column="column" :record="record" :index="index"> </slot>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { h, computed } from 'vue'
import { HolderOutlined, UndoOutlined } from '@ant-design/icons-vue'

import type { TableColumn } from '@/common/types'

import { useTableConfig } from './hooks/useTableConfig'
import useDragTable from './hooks/useDragTable'

import ColumnConfig from './components/column-config.vue'
import LineHeight from '../LineHeight/index.vue'

interface Props {
  draggable?: boolean // 是否启用拖拽
  columns: TableColumn[] // 表格列配置
  dataSource: any[] // 数据源
  scroll?: { x?: number; y?: number } // 滚动
  showColumnConfig?: boolean // 是否显示列配置
  showLineHeight?: boolean // 是否显示行高配置
  rowSelection?: false | { type?: 'checkbox' | 'radio'; [key: string]: any } // 行选择配置
  loading?: boolean // 加载状态
  pagination?: any // 分页配置
  rowKey?: string | ((record: any) => string) // 行数据的 Key
}

const props = withDefaults(defineProps<Props>(), {
  draggable: false,
  showColumnConfig: true,
  showLineHeight: true,
  rowSelection: false,
  rowKey: 'id',
  scroll: () => ({ x: 3200, y: 600 }),
  pagination: () => ({
    showTotal: (total: number) => `${total} 条记录`,
    showSizeChanger: true,
    showQuickJumper: true,
  }),
})
const emit = defineEmits(['lineHeightChange', 'selection-change', 'update:selectedRowKeys'])

const {
  visibleColumns,
  selectedRowKeys,
  updateColumns,
  setRowStyle,
  updateLineHeight,
  handleSelectionChange,
  clearSelection,
} = useTableConfig(props.columns)
// 表格唯一ID,避免页面多表格时，拖拽出现样式问题
const tableId = `drag-table-${Math.random().toString(36).slice(2, 10)}`
const { customRow } = useDragTable(props.dataSource, tableId)

const tableColumns = computed(() => {
  const cols: any[] = [...visibleColumns.value]
  if (props.draggable) {
    cols.unshift({
      key: 'dragHandle',
      width: 40,
      align: 'center',
      customRender: () => h(HolderOutlined),
    })
  }
  return cols
})
// 计算属性：分页配置
const tablePagination = computed(() => {
  return props.pagination
})

// 是否启用行选择
const isRowSelectionEnabled = computed(() => {
  if (props.rowSelection === false) return false
  if (typeof props.rowSelection === 'object' && props.rowSelection.type) {
    return ['checkbox', 'radio'].includes(props.rowSelection.type)
  }
  return false
})
// 行选择类型
const selectionType = computed(() => {
  if (!isRowSelectionEnabled.value) return null
  return (props.rowSelection as any)?.type || 'checkbox'
})
// 是否显示选择工具栏
const shouldShowSelectionToolbar = computed(() => {
  return (
    isRowSelectionEnabled.value &&
    selectionType.value === 'checkbox' &&
    selectedRowKeys.value.length > 0
  )
})

// 行选择配置
const tableRowSelection = computed(() => {
  if (!isRowSelectionEnabled.value) return null

  const baseConfig = {
    type: selectionType.value,
    selectedRowKeys: selectedRowKeys.value,
    onChange: onSelectionChange,
    getCheckboxProps: (_record: any) => ({
      disabled: false,
    }),
  }

  // 合并用户传入的其他配置
  if (typeof props.rowSelection === 'object') {
    const { type, ...otherConfig } = props.rowSelection
    return { ...baseConfig, ...otherConfig }
  }

  return baseConfig
})

/**
 * 处理行选择变化
 */
const onSelectionChange = (keys: (string | number)[], selectedRows: any[]) => {
  handleSelectionChange(keys)
  emit('selection-change', keys, selectedRows)
  emit('update:selectedRowKeys', keys)
}

/**
 * 合并行样式
 */
const mergeRowStyle = (record: any, index: number) => {
  const baseStyle = setRowStyle()

  if (!props.draggable) return baseStyle

  const dragEvents = customRow(record, index)
  return {
    ...baseStyle,
    ...dragEvents,
    style: {
      ...(baseStyle?.style || {}),
      ...(dragEvents?.style || {}),
    },
  }
}

/**
 * 处理列配置更新
 */
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  updateColumns(newColumns)
}

/**
 * 处理行高变化
 */
const handleLineHeightChange = (height: string) => {
  updateLineHeight(height as any)
  emit('lineHeightChange', height)
}
</script>

<style lang="scss" scoped>
.table-toolbar-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  margin-bottom: $spacing-md;

  .table-toolbar-left {
    display: flex;
  }
}

.selection-toolbar-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  margin-bottom: $spacing-md;

  .selection-toolbar-left {
    display: flex;
    align-items: center;
    font-weight: 500;
  }

  .selection-toolbar-right {
    display: flex;
    align-items: center;
  }
}

:deep(.ant-table-tbody > tr.target > td) {
  border-top: 2px #1890ff solid;
}
</style>
