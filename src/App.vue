<script setup lang="ts">
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import MainLayout from './layout/index.vue'

const route = useRoute()

// 判断当前路由是否需要布局
const needsLayout = computed(() => {
  return route.meta.layout !== false
})
</script>

<template>
  <!-- 根据路由配置决定是否使用布局 -->
  <MainLayout v-if="needsLayout">
    <RouterView />
  </MainLayout>
  <RouterView v-else />
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  overflow: hidden; /* 防止body出现滚动条 */
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100vh;
  width: 100vw;
  overflow: hidden; /* 防止app容器出现滚动条 */
}
</style>
