import HomeView from '@/views/home/<USER>'

export const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: {
      title: '首页',
    },
  },
  {
    path: '/produce',
    name: 'Produce',
    component: () => import('../views/produce/index.vue'),
    meta: {
      title: '生产管理',
    },
    children: [
      {
        path: 'work-order',
        name: 'ProduceWorkOrder',
        component: () => import('../views/produce/pages/worker-order/WorkOrder.vue'),
        meta: {
          title: '工单',
        },
      },
      {
        path: 'task',
        name: 'ProduceTask',
        component: () => import('../views/produce/pages/task/Task.vue'),
        meta: {
          title: '任务',
        },
      },
      {
        path: 'material-list',
        name: 'ProduceMaterialList',
        component: () => import('../views/produce/pages/material-list/MaterialList.vue'),
        meta: {
          title: '用料清单',
        },
      },
      {
        path: 'report',
        name: 'ProduceReport',
        component: () => import('../views/produce/pages/report/index.vue'),
        meta: {
          title: '报工',
        },
        children: [
          {
            path: '',
            name: 'ProduceReportList',
            component: () => import('../views/produce/pages/report/pages/Report.vue'),
          },
          {
            path: 'create',
            name: 'ProduceCreateReport',
            component: () => import('../views/produce/pages/report/pages/add-report.vue'),
            meta: {
              title: '创建报工',
            },
          },
          {
            path: 'edit/:id',
            name: 'ProduceEditReport',
            component: () => import('../views/produce/pages/report/pages/add-report.vue'),
            meta: {
              title: '编辑报工',
            },
          },
          {
            path: 'detail/:id',
            name: 'ProduceDetailReport',
            component: () => import('../views/produce/pages/report/pages/view-report.vue'),
            meta: {
              title: '报工详情',
            },
          },
        ],
      },
    ],
  },
]