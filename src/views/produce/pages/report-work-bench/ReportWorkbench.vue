<template>
  <div class="report-workbench-container">
    <!-- 左侧工作列表区域 -->
    <div class="work-list-section">
      <div class="section-header">
        <h3>待报工工作列表</h3>
        <a-button type="primary" size="small">刷新</a-button>
      </div>
      <div class="work-list-content">
        <!-- 工作列表内容 -->
        <div class="work-item" v-for="item in workList" :key="item.id" @click="selectWorkItem(item)">
          <div class="work-item-header">
            <span class="work-number">{{ item.workNumber }}</span>
            <a-tag :color="getStatusColor(item.status)">{{ item.status }}</a-tag>
          </div>
          <div class="work-item-content">
            <p class="product-name">{{ item.productName }}</p>
            <p class="work-details">
              <span>工序：{{ item.process }}</span>
              <span>数量：{{ item.quantity }}</span>
            </p>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="workList.length === 0" class="empty-state">
          <a-empty description="暂无待报工工作" />
        </div>
      </div>
    </div>

    <!-- 右侧信息填入区域 -->
    <div class="report-form-section">
      <div class="section-header">
        <h3>报工信息填写</h3>
        <a-space>
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" @click="submitReport">提交报工</a-button>
        </a-space>
      </div>
      <div class="report-form-content">
        <a-form
          :model="reportForm"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          layout="horizontal"
        >
          <a-form-item label="工单编号">
            <a-input v-model:value="reportForm.workNumber" disabled />
          </a-form-item>

          <a-form-item label="产品名称">
            <a-input v-model:value="reportForm.productName" disabled />
          </a-form-item>

          <a-form-item label="工序名称">
            <a-input v-model:value="reportForm.process" disabled />
          </a-form-item>

          <a-form-item label="生产人员" required>
            <a-select v-model:value="reportForm.operator" placeholder="请选择生产人员">
              <a-select-option value="张三">张三</a-select-option>
              <a-select-option value="李四">李四</a-select-option>
              <a-select-option value="王五">王五</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="完成数量" required>
            <a-input-number
              v-model:value="reportForm.completedQuantity"
              :min="0"
              :max="reportForm.totalQuantity"
              placeholder="请输入完成数量"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="不良数量">
            <a-input-number
              v-model:value="reportForm.defectQuantity"
              :min="0"
              placeholder="请输入不良数量"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="工时(小时)" required>
            <a-input-number
              v-model:value="reportForm.workHours"
              :min="0"
              :step="0.5"
              placeholder="请输入工时"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="备注">
            <a-textarea
              v-model:value="reportForm.remark"
              :rows="4"
              placeholder="请输入备注信息"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

// 工作列表数据类型
interface WorkItem {
  id: string
  workNumber: string
  productName: string
  process: string
  quantity: number
  status: string
}

// 报工表单数据类型
interface ReportForm {
  workNumber: string
  productName: string
  process: string
  operator: string
  completedQuantity: number | null
  defectQuantity: number | null
  workHours: number | null
  totalQuantity: number
  remark: string
}

// 模拟工作列表数据
const workList = ref<WorkItem[]>([
  {
    id: '1',
    workNumber: 'WO202501001',
    productName: '精密零件A',
    process: '车削加工',
    quantity: 100,
    status: '进行中'
  },
  {
    id: '2',
    workNumber: 'WO202501002',
    productName: '机械部件B',
    process: '铣削加工',
    quantity: 50,
    status: '待开始'
  },
  {
    id: '3',
    workNumber: 'WO202501003',
    productName: '装配件C',
    process: '装配',
    quantity: 200,
    status: '进行中'
  }
])

// 报工表单数据
const reportForm = reactive<ReportForm>({
  workNumber: '',
  productName: '',
  process: '',
  operator: '',
  completedQuantity: null,
  defectQuantity: null,
  workHours: null,
  totalQuantity: 0,
  remark: ''
})

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '进行中':
      return 'processing'
    case '待开始':
      return 'default'
    case '已完成':
      return 'success'
    default:
      return 'default'
  }
}

// 选择工作项
const selectWorkItem = (item: WorkItem) => {
  reportForm.workNumber = item.workNumber
  reportForm.productName = item.productName
  reportForm.process = item.process
  reportForm.totalQuantity = item.quantity
  reportForm.completedQuantity = null
  reportForm.defectQuantity = null
  reportForm.workHours = null
  reportForm.operator = ''
  reportForm.remark = ''
}

// 重置表单
const resetForm = () => {
  Object.assign(reportForm, {
    workNumber: '',
    productName: '',
    process: '',
    operator: '',
    completedQuantity: null,
    defectQuantity: null,
    workHours: null,
    totalQuantity: 0,
    remark: ''
  })
}

// 提交报工
const submitReport = () => {
  if (!reportForm.workNumber) {
    message.warning('请先选择工作项')
    return
  }

  if (!reportForm.operator) {
    message.warning('请选择生产人员')
    return
  }

  if (!reportForm.completedQuantity) {
    message.warning('请输入完成数量')
    return
  }

  if (!reportForm.workHours) {
    message.warning('请输入工时')
    return
  }

  // 这里应该调用API提交数据
  console.log('提交报工数据:', reportForm)
  message.success('报工提交成功')

  // 提交成功后重置表单
  resetForm()
}
</script>

<style scoped lang="scss">
.report-workbench-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.work-list-section {
  width: 40%;
  background: #f5f5f5;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;

  .section-header {
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .work-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;

    .work-item {
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }

      .work-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .work-number {
          font-weight: 600;
          color: #262626;
          font-size: 14px;
        }
      }

      .work-item-content {
        .product-name {
          font-size: 14px;
          color: #262626;
          margin-bottom: 4px;
        }

        .work-details {
          font-size: 12px;
          color: #8c8c8c;
          margin: 0;

          span {
            margin-right: 16px;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
}

.report-form-section {
  width: 60%;
  background: #fff;
  display: flex;
  flex-direction: column;

  .section-header {
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .report-form-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .ant-form {
      max-width: 600px;
    }

    .ant-form-item {
      margin-bottom: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .work-list-section {
    width: 35%;
  }

  .report-form-section {
    width: 65%;
  }
}

@media (max-width: 768px) {
  .report-workbench-container {
    flex-direction: column;
  }

  .work-list-section {
    width: 100%;
    height: 40%;
  }

  .report-form-section {
    width: 100%;
    height: 60%;
  }
}
</style>
