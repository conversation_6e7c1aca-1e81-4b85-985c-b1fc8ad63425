<template>
  <div class="report-workbench-container">
    <div class="report-workbench-header">
      <div class="report-workbench-title">报工工作台</div>
      <div class="report-workbench-user">
        <a-avatar>测</a-avatar>
        <span>测试</span>
      </div>
    </div>
    <div class="report-workbench-content">
      <!-- 左侧工作列表区域 -->
      <div class="work-list-section">
        <div class="section-header">
          <h3>今日报工</h3>
        </div>
        <div class="work-table-content">
          <a-table
            :columns="workColumns"
            :data-source="workList"
            :pagination="false"
            :customRow="customRow"
            :scroll="{ x: 700 }"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <!-- 右侧信息填入区域 -->
      <div class="report-form-section">
        <div class="section-header">
          <span>快速报工</span>
        </div>
        <div class="report-form-content">
          <a-form
            :model="reportForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="vertical"
          >
            <a-form-item label="工单编号">
              <a-input v-model:value="reportForm.workNumber" disabled />
            </a-form-item>

            <a-form-item label="产品名称">
              <a-input v-model:value="reportForm.productName" disabled />
            </a-form-item>

            <a-form-item label="工序名称">
              <a-input v-model:value="reportForm.process" disabled />
            </a-form-item>

            <a-form-item label="生产人员" required>
              <a-select v-model:value="reportForm.operator" placeholder="请选择生产人员">
                <a-select-option value="张三">张三</a-select-option>
                <a-select-option value="李四">李四</a-select-option>
                <a-select-option value="王五">王五</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="完成数量" required>
              <a-input-number
                v-model:value="reportForm.completedQuantity"
                :min="0"
                :max="reportForm.totalQuantity"
                placeholder="请输入完成数量"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="不良数量">
              <a-input-number
                v-model:value="reportForm.defectQuantity"
                :min="0"
                placeholder="请输入不良数量"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="工时(小时)" required>
              <a-input-number
                v-model:value="reportForm.workHours"
                :min="0"
                :step="0.5"
                placeholder="请输入工时"
                style="width: 100%"
              />
            </a-form-item>

            <a-form-item label="备注">
              <a-textarea
                v-model:value="reportForm.remark"
                :rows="4"
                placeholder="请输入备注信息"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { locale } from 'dayjs'

// 工作列表数据类型
interface WorkItem {
  id: string
  workNumber: string
  productName: string
  process: string
  quantity: number
  status: string
}

// 报工表单数据类型
interface ReportForm {
  workNumber: string
  productName: string
  process: string
  operator: string
  completedQuantity: number | null
  defectQuantity: number | null
  workHours: number | null
  totalQuantity: number
  remark: string
}

// 表格列定义
const workColumns: TableColumnsType<WorkItem> = [
  {
    title: '工单编号',
    dataIndex: 'workNumber',
    key: 'workNumber',
    width: 120,
    fixed: 'left',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    width: 120,
  },
  {
    title: '工序名称',
    dataIndex: 'process',
    key: 'process',
    width: 100,
  },
  {
    title: '生产人员',
    dataIndex: 'worker',
    key: 'worker',
    width: 100,
  },
  {
    title: '良品数',
    dataIndex: 'goodCount',
    key: 'goodCount',
    width: 80,
  },
  {
    title: '不良品数',
    dataIndex: 'badCount',
    key: 'badCount',
    width: 80,
  },
  {
    title: '报工创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
]

// 模拟工作列表数据
const workList = ref<WorkItem[]>([])
// const workList = ref<WorkItem[]>([
//   {
//     id: '1',
//     workNumber: 'WO202501001',
//     productName: '精密零件A',
//     process: '车削加工',
//     quantity: 100,
//     status: '进行中',
//   },
//   {
//     id: '2',
//     workNumber: 'WO202501002',
//     productName: '机械部件B',
//     process: '铣削加工',
//     quantity: 50,
//     status: '待开始',
//   },
//   {
//     id: '3',
//     workNumber: 'WO202501003',
//     productName: '装配件C',
//     process: '装配',
//     quantity: 200,
//     status: '进行中',
//   },
// ])

// 报工表单数据
const reportForm = reactive<ReportForm>({
  workNumber: '',
  productName: '',
  process: '',
  operator: '',
  completedQuantity: null,
  defectQuantity: null,
  workHours: null,
  totalQuantity: 0,
  remark: '',
})

const customRow = () => {
  return {
    style: {
      height: '64px',
    },
  }
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '进行中':
      return 'processing'
    case '待开始':
      return 'default'
    case '已完成':
      return 'success'
    default:
      return 'default'
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(reportForm, {
    workNumber: '',
    productName: '',
    process: '',
    operator: '',
    completedQuantity: null,
    defectQuantity: null,
    workHours: null,
    totalQuantity: 0,
    remark: '',
  })
}

// 提交报工
const submitReport = () => {
  if (!reportForm.workNumber) {
    message.warning('请先选择工作项')
    return
  }

  if (!reportForm.operator) {
    message.warning('请选择生产人员')
    return
  }

  if (!reportForm.completedQuantity) {
    message.warning('请输入完成数量')
    return
  }

  if (!reportForm.workHours) {
    message.warning('请输入工时')
    return
  }

  // 这里应该调用API提交数据
  console.log('提交报工数据:', reportForm)
  message.success('报工提交成功')

  // 提交成功后重置表单
  resetForm()
}
</script>

<style scoped lang="scss">
.report-workbench-container {
  height: 100vh; // 全屏高度
  width: 100vw; // 全屏宽度
  margin: 0;
  padding: 0;
  overflow: hidden;

  .report-workbench-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;

    div.report-workbench-title {
      color: #1b1b1b;
      font-size: 20px;
      font-weight: 500;
      line-height: 20px;
    }

    .report-workbench-user {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  .report-workbench-content {
    display: flex;
    height: 100%;
  }
}

.work-list-section {
  flex: 1;
  min-width: 600px;
  max-width: 100%;
  margin: 0 20px;
  position: relative;
  overflow: auto;

  .section-header {
    color: #1b1b1b;
    padding-top: 24px;
    padding-bottom: 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: 20px;
  }

  .work-table-content {
    flex: 1;
    overflow: hidden;
    background: #fff;

    :deep(.ant-table-wrapper) {
      height: 100%;

      .ant-table {
        height: 100%;
      }

      .ant-table-tbody > tr {
        cursor: pointer;

        &:hover {
          background-color: #f5f5f5;
        }

        &.ant-table-row-selected {
          background-color: #e6f7ff;
        }
      }
    }
  }
}

.report-form-section {
  border-left: 1px solid #e5e5e5;
  width: 35%;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 24px;

    span {
      color: #1b1b1b;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: break-all;
      width: 80px;
      padding-top: 10px;
      padding-bottom: 10px;
      font-size: 20px;
      font-weight: 500;
      line-height: 1;
      overflow: hidden;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .report-form-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;

    .ant-form {
      max-width: 600px;
    }

    .ant-form-item {
      margin-bottom: 20px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .work-list-section {
    width: 35%;
  }

  .report-form-section {
    width: 65%;
  }
}

@media (max-width: 768px) {
  .report-workbench-container {
    flex-direction: column;
  }

  .work-list-section {
    width: 100%;
    height: 40%;
  }

  .report-form-section {
    width: 100%;
    height: 60%;
  }
}
</style>
